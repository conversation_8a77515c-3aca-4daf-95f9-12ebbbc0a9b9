'use client';

import { useTranslations } from 'next-intl';
import Button from '@/components/ui/Button';

const HeroSection = () => {
  const t = useTranslations('hero');

  return (
    <section className="relative h-screen py-20 flex items-end justify-center overflow-hidden">
      {/* Hero Background Image */}
      <div
        className="absolute inset-0 z-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: 'url(/images/hero.png)'
        }}
      ></div>

      <div className="relative w-full mt-10 max-w-7xl mx-auto px-6 lg:px-8 z-20">
        <div className="space-y-8">
          {/* Main Title - Bold and Impactful */}
          <div className="space-y-8">
            <h1 className="text-6xl">
              <span className="block max-w-4xl text-albatros-ivory mb-4">
                {t('title')}
              </span>
            </h1>

            <div className="max-w-3xl">
              <p className="text-xl md:text-lg text-albatros-ivory leading-relaxed font-light">
                {t('description')}
              </p>
            </div>
          </div>

          {/* Enhanced CTA with modern styling */}
          <div className="flex flex-col sm:flex-row gap-6">
            <Button variant="white" size="md">
              {t('contactButton')}
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
