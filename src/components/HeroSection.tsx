'use client';

import { useTranslations } from 'next-intl';
import { useInView } from 'react-intersection-observer';
import Button from '@/components/ui/Button';

const HeroSection = () => {
  const t = useTranslations('hero');

  // Intersection observers for staggered animations
  const { ref: titleRef, inView: titleInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: descriptionRef, inView: descriptionInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: buttonRef, inView: buttonInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  return (
    <section className="relative h-screen py-20 flex items-end justify-center overflow-hidden">
      {/* Hero Background Image */}
      <div
        className="absolute inset-0 z-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: 'url(/images/hero.png)'
        }}
      ></div>

      <div className="relative w-full mt-10 max-w-7xl mx-auto px-6 lg:px-8 z-20">
        <div className="space-y-8">
          {/* Main Title - Bold and Impactful */}
          <div className="space-y-8">
            <h1
              ref={titleRef}
              className={`text-6xl block max-w-2xl text-albatros-ivory mb-4 transition-all duration-1000 ease-out ${
                titleInView
                  ? 'opacity-100 translate-y-0'
                  : 'opacity-0 translate-y-8'
              }`}
            >
              {t('title')}
            </h1>

            <div
              ref={descriptionRef}
              className={`max-w-2xl transition-all duration-1000 ease-out delay-300 ${
                descriptionInView
                  ? 'opacity-100 translate-y-0'
                  : 'opacity-0 translate-y-8'
              }`}
            >
              <p className="text-xl md:text-lg text-albatros-ivory leading-relaxed font-light">
                {t('description')}
              </p>
            </div>
          </div>

          {/* Enhanced CTA with modern styling */}
          <div
            ref={buttonRef}
            className={`flex flex-col sm:flex-row gap-6 transition-all duration-1000 ease-out delay-600 ${
              buttonInView
                ? 'opacity-100 translate-y-0'
                : 'opacity-0 translate-y-8'
            }`}
          >
            <Button variant="white" size="md">
              {t('contactButton')}
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
